import { type Flag } from "@/src/features/feature-flags/types";
import { type ProjectScope } from "@/src/features/rbac/constants/projectAccessRights";
import {
  Database,
  LayoutDashboard,
  LifeBuoy,
  ListTree,
  type LucideIcon,
  Settings,
  UsersIcon,
  TerminalIcon,
  Lightbulb,
  Grid2X2,
  Sparkle,
  FileJson,
  Search,
  Home,
  SquarePercent,
  ClipboardPen,
  Clock,
  AppWindow,
  Building2,
  Gauge,
  Shield,
} from "lucide-react";
import { type ReactNode } from "react";
import { type Entitlement } from "@/src/features/entitlements/constants/entitlements";
import { type User } from "next-auth";
import { type OrganizationScope } from "@/src/features/rbac/constants/organizationAccessRights";
// 白标化版本：移除支持菜单导入
import { SidebarMenuButton } from "@/src/components/ui/sidebar";
import { useCommandMenu } from "@/src/features/command-k-menu/CommandMenuProvider";
import { usePostHogClientCapture } from "@/src/features/posthog-analytics/usePostHogClientCapture";
import { CloudStatusMenu } from "@/src/features/cloud-status-notification/components/CloudStatusMenu";
import { type ProductModule } from "@/src/ee/features/ui-customization/productModuleSchema";
import { useTranslation } from "next-i18next";

export enum RouteSection {
  Main = "main",
  Secondary = "secondary",
}

export enum RouteGroup {
  Observability = "Observability",
  PromptManagement = "Prompt Management",
  Evaluation = "Evaluation",
  RegistrationManagement = "Registration Management",
}

export type Route = {
  title: string;
  menuNode?: ReactNode;
  featureFlag?: Flag;
  label?: string | ReactNode;
  projectRbacScopes?: ProjectScope[]; // array treated as OR
  organizationRbacScope?: OrganizationScope;
  icon?: LucideIcon; // ignored for nested routes
  pathname: string; // link
  items?: Array<Route>; // folder
  section?: RouteSection; // which section of the sidebar (top/main/bottom)
  newTab?: boolean; // open in new tab
  entitlements?: Entitlement[]; // entitlements required, array treated as OR
  productModule?: ProductModule; // Product module this route belongs to. Used to show/hide modules via ui customization.
  show?: (p: {
    organization: User["organizations"][number] | undefined;
  }) => boolean;
  group?: RouteGroup; // group this route belongs to (within a section)
};

export const ROUTES: Route[] = [
  {
    title: "Go to...",
    pathname: "", // Empty pathname since this is a dropdown
    icon: Search,
    menuNode: <CommandMenuTrigger />,
    section: RouteSection.Main,
  },
  {
    title: "Organizations",
    pathname: "/",
    icon: Grid2X2,
    show: ({ organization }) => organization === undefined,
    section: RouteSection.Main,
  },
  {
    title: "Projects",
    pathname: "/organization/[organizationId]",
    icon: Grid2X2,
    section: RouteSection.Main,
  },
  {
    title: "Home",
    pathname: `/project/[projectId]`,
    icon: Home,
    section: RouteSection.Main,
  },
  {
    title: "Dashboards",
    pathname: `/project/[projectId]/dashboards`,
    icon: LayoutDashboard,
    productModule: "dashboards",
    section: RouteSection.Main,
  },
  {
    title: "Tracing",
    icon: ListTree,
    productModule: "tracing",
    group: RouteGroup.Observability,
    section: RouteSection.Main,
    pathname: `/project/[projectId]/traces`,
  },
  {
    title: "Sessions",
    icon: Clock,
    productModule: "tracing",
    group: RouteGroup.Observability,
    section: RouteSection.Main,
    pathname: `/project/[projectId]/sessions`,
  },
  {
    title: "Users",
    pathname: `/project/[projectId]/users`,
    icon: UsersIcon,
    productModule: "tracing",
    group: RouteGroup.Observability,
    section: RouteSection.Main,
  },
  {
    title: "Prompts",
    pathname: "/project/[projectId]/prompts",
    icon: FileJson,
    projectRbacScopes: ["prompts:read"],
    productModule: "prompt-management",
    group: RouteGroup.PromptManagement,
    section: RouteSection.Main,
  },
  {
    title: "Playground",
    pathname: "/project/[projectId]/playground",
    icon: TerminalIcon,
    productModule: "playground",
    group: RouteGroup.PromptManagement,
    section: RouteSection.Main,
  },
  {
    title: "Scores",
    pathname: `/project/[projectId]/scores`,
    group: RouteGroup.Evaluation,
    section: RouteSection.Main,
    icon: SquarePercent,
  },
  {
    title: "LLM-as-a-Judge",
    icon: Lightbulb,
    productModule: "evaluation",
    projectRbacScopes: ["evalJob:read"],
    group: RouteGroup.Evaluation,
    section: RouteSection.Main,
    pathname: `/project/[projectId]/evals`,
  },
  {
    title: "Human Annotation",
    pathname: `/project/[projectId]/annotation-queues`,
    projectRbacScopes: ["annotationQueues:read"],
    group: RouteGroup.Evaluation,
    section: RouteSection.Main,
    icon: ClipboardPen,
  },
  {
    title: "Datasets",
    pathname: `/project/[projectId]/datasets`,
    icon: Database,
    productModule: "datasets",
    group: RouteGroup.Evaluation,
    section: RouteSection.Main,
  },
  {
    title: "Application Registration",
    pathname: `/project/[projectId]/registration/applications`,
    icon: AppWindow,
    group: RouteGroup.RegistrationManagement,
    section: RouteSection.Main,
  },
  {
    title: "Tenant Registration",
    pathname: `/tenant-management`,
    icon: Building2,
    group: RouteGroup.RegistrationManagement,
    section: RouteSection.Main,
  },
  {
    title: "Quota Management",
    pathname: `/project/[projectId]/registration/quotas`,
    icon: Gauge,
    group: RouteGroup.RegistrationManagement,
    section: RouteSection.Main,
  },
  {
    title: "Authorization Management",
    pathname: `/project/[projectId]/registration/authorizations`,
    icon: Shield,
    group: RouteGroup.RegistrationManagement,
    section: RouteSection.Main,
  },
  // 白标化版本：移除升级按钮
  {
    title: "Cloud Status",
    section: RouteSection.Secondary,
    pathname: "",
    menuNode: <CloudStatusMenu />,
  },
  {
    title: "Settings",
    pathname: "/project/[projectId]/settings",
    icon: Settings,
    section: RouteSection.Secondary,
  },
  {
    title: "Settings",
    pathname: "/organization/[organizationId]/settings",
    icon: Settings,
    section: RouteSection.Secondary,
  },
  // 白标化版本：移除支持按钮
];

function CommandMenuTrigger() {
  const { setOpen } = useCommandMenu();
  const capture = usePostHogClientCapture();
  const { t } = useTranslation("common");

  return (
    <SidebarMenuButton
      onClick={() => {
        capture("cmd_k_menu:opened", {
          source: "main_navigation",
        });
        setOpen(true);
      }}
      className="whitespace-nowrap"
    >
      <Search className="h-4 w-4" />
      {t("goTo")}
      <kbd className="pointer-events-none ml-auto inline-flex h-5 select-none items-center gap-1 rounded-md border px-1.5 font-mono text-[10px]">
        {navigator.userAgent.includes("Mac") ? (
          <span className="text-[12px]">⌘</span>
        ) : (
          <span>Ctrl</span>
        )}
        <span>K</span>
      </kbd>
    </SidebarMenuButton>
  );
}

// Function to get translated routes
export function useTranslatedRoutes(): Route[] {
  const { t } = useTranslation("common");

  return ROUTES.map((route) => ({
    ...route,
    title: getTranslatedRouteTitle(route.title, t),
  }));
}

// Helper function to translate route titles
function getTranslatedRouteTitle(title: string, t: any): string {
  const titleMap: Record<string, string> = {
    "Go to...": t("goTo", "Go to..."),
    Organizations: t("organizations", "Organizations"),
    Projects: t("projects", "Projects"),
    Home: t("home", "Home"),
    Dashboards: t("dashboards", "Dashboards"),
    Tracing: t("tracing", "Tracing"),
    Sessions: t("sessions", "Sessions"),
    Users: t("users", "Users"),
    Prompts: t("prompts", "Prompts"),
    Playground: t("playground", "Playground"),
    Scores: t("scores", "Scores"),
    "LLM-as-a-Judge": t("llmAsJudge", "LLM-as-a-Judge"),
    "Human Annotation": t("humanAnnotation", "Human Annotation"),
    Datasets: t("datasets", "Datasets"),
    "Application Registration": t(
      "applicationRegistration",
      "Application Registration",
    ),
    "Tenant Registration": t("tenantRegistration", "Tenant Registration"),
    "Quota Management": t("quotaManagement", "Quota Management"),
    "Authorization Management": t(
      "authorizationManagement",
      "Authorization Management",
    ),
    "Cloud Status": t("cloudStatus", "Cloud Status"),
    Settings: t("settings", "Settings"),
    // 白标化版本：移除升级和支持按钮的翻译
  };

  return titleMap[title] || title;
}
